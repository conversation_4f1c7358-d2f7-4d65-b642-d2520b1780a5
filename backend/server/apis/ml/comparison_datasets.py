import json
from http import HTTPStatus
from typing import Optional

from flask import Response, jsonify, request
from pydantic import BaseModel, ConfigDict, ValidationError

from server import db, tasks, utils
from server.routes import route


@route(["ml"], "/comparison/dataset", methods=["GET"])
def get_dataset() -> Response:
    return Response({"message": "Not implemented"})


class PostDatasetRequest(BaseModel):
    splits: bool
    name: Optional[str] = None


@route(["ml"], "/comparison/dataset", methods=["POST"])
def post_dataset() -> Response:
    data = json.loads(request.data)

    try:
        api_request = PostDatasetRequest(**data)
    except ValidationError as e:
        return Response(f"Failed to create dataset: {str(e)}", HTTPStatus.INTERNAL_SERVER_ERROR)

    dataset_id = db.utils.uuid4_str()
    created = utils.epoch_timestamp_ms()

    if api_request.name is None:
        name = f"dataset v2 - {created}"
    else:
        name = api_request.name

    task = tasks.create_comparison_dataset.delay(dataset_id)

    redis_client = utils.get_redis()
    redis_client.hset("dataset_tasks", dataset_id, task.id)
    return jsonify({"id": dataset_id, "name": name})
