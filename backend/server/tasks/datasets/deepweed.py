import datetime
import enum
import json
import logging
import math
import os
from typing import Any, Dict, List, Optional, Set, Tuple, cast

import boto3
import pandas
from kombu.utils.json import register_type

from server import config, constants, utils
from server.constants import CARBON_ML_BUCKET, DSV2_PAGE_SIZE_LIMIT
from server.dataset_utils import MiniPerfTracker, create_dataset_and_push_to_s3
from server.db import queries, tables
from server.tasks.app import app_task
from server.tasks.constants import CeleryQueue
from server.tasks.dataset_types import (
    DatasetCategories,
    DatasetDatapoint,
    DatasetMetadata,
    DatasetOptions,
    DatasetPoint,
    DatasetPolygon,
    DatasetStatistics,
    Split,
)

LOG = logging.getLogger(__name__)


register_type(
    DatasetOptions,
    marker="dataset_options",
    encoder=lambda x: x.model_dump_json(),
    decoder=lambda x: DatasetOptions(**json.loads(x)),
)


class SplitSelector:
    def __init__(
        self,
        dataset_options: DatasetOptions,
        train_percentage: float = 0.7,
        validation_percentage: float = 0.15,
        test_percentage: float = 0.15,
        train_limit: Optional[int] = None,
        val_limit: Optional[int] = None,
        test_limit: Optional[int] = None,
        datapoints_limit: Optional[int] = None,
        parent_dataset_filepaths: Optional[dict[str, str]] = None,
    ):
        self._dataset_options = dataset_options
        self._split_percentages = {
            Split.TRAIN: train_percentage,
            Split.VALIDATION: validation_percentage,
            Split.TEST: test_percentage,
        }
        self._parent_dataset_filepaths = parent_dataset_filepaths

        self._total_item_counts: dict[Split, int] = {
            Split.TRAIN: 0,
            Split.VALIDATION: 0,
            Split.TEST: 0,
            Split.DATAPOINTS: 0,
        }

        self._driptape_counts: dict[Split, int] = {
            Split.TRAIN: 0,
            Split.VALIDATION: 0,
            Split.TEST: 0,
        }

        self._driptape_percentages: dict[Split, Optional[float]] = {
            Split.TRAIN: dataset_options.train_driptape_percentage,
            Split.VALIDATION: dataset_options.val_driptape_percentage,
            Split.TEST: dataset_options.test_driptape_percentage,
        }

        self._split_geohashes: dict[Split, Set[str]] = {
            Split.TRAIN: set(dataset_options.train_geohashes or []),
            Split.VALIDATION: set(dataset_options.val_geohashes or []),
            Split.TEST: set(dataset_options.test_geohashes or []),
        }

        geohash_lens = [len(geohash) for geohash in set().union(*self._split_geohashes.values())]
        self._geohash_len = 0
        if len(geohash_lens) > 0:
            assert all(
                geohash_len == geohash_lens[0] for geohash_len in geohash_lens
            ), "Geohashes in split geohashes must be the same length"
            self._geohash_len = geohash_lens[0]

        self._split_limits: dict[Split, int] = {}

        if not self._dataset_options.splits:
            if datapoints_limit is not None:
                self._split_limits[Split.DATAPOINTS] = datapoints_limit
        else:
            if train_limit is not None:
                self._split_limits[Split.TRAIN] = train_limit

            if val_limit is not None:
                self._split_limits[Split.VALIDATION] = val_limit

            if test_limit is not None:
                self._split_limits[Split.TEST] = test_limit

        self._train_total_items = 0

        self._bucket_counts: dict[str, pandas.DataFrame] = {}
        self._parent_dataset_image_ids: dict[str, Split] = {}

        if self._parent_dataset_filepaths is not None:
            for name, filepath in self._parent_dataset_filepaths.items():
                if name == "metadata":
                    continue

                split = Split(name)

                with open(filepath, "r") as f:
                    for line in f.readlines():
                        datapoint = DatasetDatapoint(**json.loads(line))
                        self._parent_dataset_image_ids[datapoint.image_id] = split

    @property
    def split_limits(self) -> dict[Split, int]:
        return self._split_limits

    @property
    def total_item_counts(self) -> dict[Split, int]:
        return self._total_item_counts

    def update_split_counts(self, split: Split, count_diff: int) -> None:
        self._total_item_counts[split] += count_diff

    def geohash_in_split(self, geohash: str, split: Split) -> bool:
        if len(self._split_geohashes[split]) == 0:
            return True

        return geohash[: self._geohash_len] in self._split_geohashes[split]

    def __call__(self, datapoint: DatasetDatapoint) -> tuple[Optional[Split], bool]:
        is_new = True
        split = None
        has_driptape = len(datapoint.polygons) > 0

        if sum(self._split_percentages.values()) == 0:
            return None, False

        if datapoint.categories is not None and "driptape" not in json.loads(datapoint.categories.data).get(
            "polygonLabels", {}
        ):
            return None, False

        if self._dataset_options.splits:
            if (
                datapoint.label_id is None
                and self._dataset_options.unlabeled_data
                and self._total_item_counts[Split.TRAIN] < self._split_limits.get(Split.TRAIN, math.inf)
            ):
                split = Split.TRAIN

                if not has_driptape and self._total_item_counts[split] > 0 and self._driptape_percentages[split]:
                    current_driptape_ratio = self._driptape_counts[split] / self._total_item_counts[split]
                    if current_driptape_ratio > self._driptape_percentages[split]:  # type: ignore
                        split = None
            else:
                parent_split = self._parent_dataset_image_ids.get(datapoint.image_id)

                if (
                    self._dataset_options.evaluation
                    and parent_split is None
                    and self.geohash_in_split(datapoint.geohash, Split.TEST)
                ):
                    return Split.TEST, True
                elif self._dataset_options.evaluation:
                    return None, False

                geohash_prefix = datapoint.geohash[:6]
                date = datetime.datetime.fromtimestamp(datapoint.captured_at / 1000).date()
                crop_id = datapoint.crop_id
                point_category_ids = list(set(point.point_category_id for point in datapoint.points))

                bucket_key = f"{crop_id}-{geohash_prefix}-{date}"
                if bucket_key not in self._bucket_counts.keys():
                    self._bucket_counts[bucket_key] = pandas.DataFrame([], columns=["train", "validation", "test"])

                counts_df = self._bucket_counts[bucket_key]

                scores: dict[Split, float] = {}
                counts: dict[Split, pandas.DataFrame] = {}
                for split in [Split.TRAIN, Split.VALIDATION, Split.TEST]:
                    score, df = self._compute_bucket_scores(counts_df, point_category_ids, split=split)

                    scores[split] = score
                    counts[split] = df

                if parent_split is None:
                    min = math.inf
                    split = None
                    for spl in [Split.TRAIN, Split.VALIDATION, Split.TEST]:
                        if scores.get(spl, 0.0) < min:
                            if self._total_item_counts[spl] < self._split_limits.get(spl, math.inf):
                                if not self.geohash_in_split(datapoint.geohash, spl):
                                    continue
                                split = spl
                                min = scores.get(spl, 0.0)

                else:
                    if not self.geohash_in_split(datapoint.geohash, parent_split):
                        parent_split = Split.TRAIN
                    if self._total_item_counts[parent_split] < self._split_limits.get(parent_split, math.inf):
                        split = parent_split
                        is_new = False
                    else:
                        split = None
                if split is not None:
                    if not has_driptape and self._total_item_counts[split] > 0 and self._driptape_percentages[split]:
                        current_driptape_ratio = self._driptape_counts[split] / self._total_item_counts[split]
                        if current_driptape_ratio < self._driptape_percentages[split]:  # type: ignore
                            split = None
                        else:
                            self._bucket_counts[bucket_key] = counts[split]
                    else:
                        self._bucket_counts[bucket_key] = counts[split]

            if split is not None:
                self._total_item_counts[split] += 1
                if has_driptape:
                    self._driptape_counts[split] += 1

                if self._total_item_counts[split] >= self._split_limits.get(split, math.inf):
                    self._split_percentages[split] = 0  # Rescale pecentages if limit is reached
                    total = sum(self._split_percentages.values())
                    if total != 0:
                        for s in [Split.TRAIN, Split.VALIDATION, Split.TEST]:
                            self._split_percentages[s] = self._split_percentages[s] / total

        else:
            if self._total_item_counts[Split.DATAPOINTS] < self._split_limits.get(Split.DATAPOINTS, math.inf):
                split = Split.DATAPOINTS
                self._total_item_counts[split] += 1

        return split, is_new

    def _compute_bucket_scores(
        self, counts_df: pandas.DataFrame, point_category_ids: list[str], split: Split
    ) -> Tuple[float, pandas.DataFrame]:
        df = self._make_dataframe(point_category_ids, split=split)

        updated_counts_df = counts_df.add(df, fill_value=0)
        df = updated_counts_df.loc[point_category_ids]
        df_sum = df.sum(axis=1)
        df[df_sum != 0] = df[df_sum != 0].div(df_sum[df_sum != 0], axis=0)
        df[Split.TRAIN.value] = abs(df[Split.TRAIN.value] - self._split_percentages[Split.TRAIN])
        df[Split.VALIDATION.value] = abs(df[Split.VALIDATION.value] - self._split_percentages[Split.VALIDATION])
        df[Split.TEST.value] = abs(df[Split.TEST.value] - self._split_percentages[Split.TEST])
        df = df.sum(axis=1)

        score: float = df.sum()

        return score, updated_counts_df

    def _make_dataframe(self, point_category_ids: list[str], split: Split) -> pandas.DataFrame:
        df = pandas.DataFrame(
            index=point_category_ids,
            columns=[Split.TRAIN.value, Split.VALIDATION.value, Split.TEST.value],
            data=[[0] * 3] * len(point_category_ids),
        )

        df[split.value] = 1

        return df


def _construct_datapoint(image: tables.Image) -> DatasetDatapoint:
    try:
        task = image.tasks[0]
        labels = [label for label in task.labels if label.valid == 1 and label.workflow == tables.Label.WORKFLOW_REVIEW]

        if len(labels) == 0:
            label = None
        elif len(labels) == 1:
            label = labels[0]
        else:
            label = sorted(labels, key=lambda label: label.created, reverse=True)[0]

        points: list[DatasetPoint] = []
        polygons: list[DatasetPolygon] = []
        categories: Optional[DatasetCategories] = None
        if label is not None:
            for point in label.point_objs:
                points.append(
                    DatasetPoint(
                        id=point.id,
                        x=point.x,
                        y=point.y,
                        radius=point.radius,
                        confidence=point.confidence,
                        point_category_id=point.point_category_id,
                    )
                )

            if label.data is not None:
                for polygon in label.data.get("polygons", []):
                    polygons.append(
                        DatasetPolygon(
                            id=polygon.get("id"),
                            label=polygon.get("label"),
                            confidence=polygon.get("confidence"),
                            coordinates=polygon.get("coordinates"),
                        )
                    )

            if label.categories_obj is not None:
                categories = DatasetCategories(
                    id=label.categories_obj.id,
                    data=label.categories_obj.data,
                )

        try:
            geo_json = json.loads(image.geo_json.rstrip())
            latitude: float = geo_json["lla"]["lat"]
            longitude: float = geo_json["lla"]["lng"]
        except:
            raise RuntimeError(f"Failed to find geo information for image: {image.id}")

        datapoint = DatasetDatapoint(
            image_id=image.id,
            task_id=task.id,
            label_id=label.id if label else None,
            uri=image.url,
            width=image.width,
            height=image.height,
            is_new=False,
            row_id=image.row_id,
            cam_id=image.cam_id,
            capture_session_id=image.capture_session_id,
            crop_id=image.crop_id,
            robot_id=image.robot_id,
            captured_at=image.captured_at,
            geohash=image.geohash,
            latitude=latitude,
            longitude=longitude,
            ppi=image.ppi,
            points=points,
            polygons=polygons,
            categories=categories,
        )
    except Exception as e:
        LOG.info(f"Failed to construct datapoint: image_id={image.id}")
        raise e

    return datapoint


def _add_bucket_prefix(bucket: str, key: str) -> str:
    return f"s3://{bucket}/{key}"


@app_task(queue=CeleryQueue.DATASETS.value)
def create_dataset(dataset_id: str, name: str, created_at: int, arguments: Dict[str, Any]) -> None:
    (
        train_path,
        train_label_ids,
        validation_path,
        validation_label_ids,
        test_path,
        test_label_ids,
    ) = create_dataset_and_push_to_s3(
        name=name,
        dataset_id=dataset_id,
        parent_id=arguments.get("parent_id"),
        created=created_at,
        crops=arguments.get("crops"),
        crop_ids=arguments.get("crop_ids"),
        robots=arguments.get("robots"),
        start_timestamp=arguments.get("start_timestamp"),
        stop_timestamp=arguments.get("stop_timestamp"),
        fast_run=arguments.get("fast_run"),
        evaluation=arguments.get("evaluation"),
        balance_positive_classes=arguments.get("balance_positive_classes"),
        balance_robot_ids=arguments.get("balance_robot_ids"),
        balance_geohash=arguments.get("balance_geohash"),
        balance_density=arguments.get("balance_density"),
        exclude_robots=arguments.get("exclude_robots"),
        exclude_crop_ids=arguments.get("exclude_crop_ids"),
        geohash=arguments.get("geohash"),
        only_new_data=arguments.get("only_new_data"),
        val_test_crop_ids=arguments.get("val_test_crop_ids"),
        core_data_params=arguments.get("core_data_params"),
        flag_core_data=arguments.get("flag_core_data"),
        test_set_geohashes=arguments.get("test_set_geohashes"),
        train_set_size_limit=arguments.get("train_set_size_limit"),
        validation_set_size_limit=arguments.get("validation_set_size_limit"),
        test_set_size_limit=arguments.get("test_set_size_limit"),
        val_test_geohashes=arguments.get("val_test_geohashes"),
        driptape=cast(bool, arguments.get("driptape")),
        val_no_driptape_keep_multiplier=arguments.get("val_no_driptape_keep_multiplier"),
        test_no_driptape_keep_multiplier=arguments.get("test_no_driptape_keep_multiplier"),
        train_no_driptape_keep_multiplier=arguments.get("train_no_driptape_keep_multiplier"),
        image_ids=arguments.get("image_ids"),
    )

    queries.dataset.create(
        id=dataset_id,
        created=created_at,
        name=name,
        train=train_path,
        validation=validation_path,
        test=test_path,
        parent_id=arguments.get("parent_id"),
        num_images=len(train_label_ids) + len(validation_label_ids) + len(test_label_ids),
        fast_run=arguments.get("fast_run", False),
    )

    queries.dataset_labels.create(dataset_id, train_label_ids, validation_label_ids, test_label_ids)

    if config.POPULATE_CAPTURE_SESSION == 1:
        queries.capture_sessions.associate_capture_sessions_to_dataset(dataset_id)

    utils.get_redis().hdel("dataset_tasks", dataset_id)
    utils.get_redis().hdel("dataset_tasks", name)


@app_task(queue=CeleryQueue.DATASETS.value)
def create_dataset_v2(
    dataset_id: str,
    name: str,
    created: int,
    dataset_options: DatasetOptions,
    page_size: int = 1000,
    verbose: bool = False,
) -> None:
    LOG.info(f"Generating dataset {dataset_id} from the following options: {dataset_options}")
    assert page_size <= DSV2_PAGE_SIZE_LIMIT, f"Page size over limit of {DSV2_PAGE_SIZE_LIMIT}"
    start_timestamp = utils.epoch_timestamp_ms()

    perf_tracker = MiniPerfTracker()

    data_dir = f"/data/{os.getpid()}"

    # Set dataset keys in S3 based on the dataset_id
    s3_prefix = f"datasets/{dataset_id}"  # specifies the s3 directory instead of each splits file in the directory
    dataset_s3_keys = {
        "metadata": _add_bucket_prefix(constants.CARBON_ML_BUCKET, f"{s3_prefix}/metadata.json"),
        "datapoints": _add_bucket_prefix(constants.CARBON_ML_BUCKET, f"{s3_prefix}/datapoints.jsonl"),
        "train": _add_bucket_prefix(constants.CARBON_ML_BUCKET, f"{s3_prefix}/train.jsonl"),
        "validation": _add_bucket_prefix(constants.CARBON_ML_BUCKET, f"{s3_prefix}/validation.jsonl"),
        "test": _add_bucket_prefix(constants.CARBON_ML_BUCKET, f"{s3_prefix}/test.jsonl"),
    }

    if dataset_options.splits:
        dataset_filepaths = {
            "metadata": f"{data_dir}/datasets/{dataset_id}/metadata.json",
            "train": f"{data_dir}/datasets/{dataset_id}/train.jsonl",
            "validation": f"{data_dir}/datasets/{dataset_id}/validation.jsonl",
            "test": f"{data_dir}/datasets/{dataset_id}/test.jsonl",
        }
    else:
        dataset_filepaths = {
            "metadata": f"{data_dir}/datasets/{dataset_id}/metadata.json",
            "datapoints": f"{data_dir}/datasets/{dataset_id}/datapoints.jsonl",
        }

    for filepath in dataset_filepaths.values():
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

    # If there is a parent ID then we need to fetch the dataset metadata and options, validate that we can build a
    # child dataset from the parent dataset and then create our starting point dataset from the parent dataset.
    with perf_tracker.timer("Setting up parent dataset"):
        if dataset_options.parent_dataset_id is not None:
            parent_dataset = queries.dataset.get_dataset(dataset_id=dataset_options.parent_dataset_id)

            if parent_dataset is None:
                raise RuntimeError(f"Parent dataset {dataset_options.parent_dataset_id} not found")

            if parent_dataset.s3_prefix is None:
                raise RuntimeError(f"Parent dataset {dataset_options.parent_dataset_id} has no s3_prefix")
            if not parent_dataset.s3_prefix.startswith("s3://"):
                parent_dataset.s3_prefix = f"s3://{CARBON_ML_BUCKET}/{parent_dataset.s3_prefix}"
            parent_dataset_s3_keys = {
                "metadata": f"{parent_dataset.s3_prefix}/metadata.json",
                "datapoints": f"{parent_dataset.s3_prefix}/datapoints.jsonl",
                "train": f"{parent_dataset.s3_prefix}/train.jsonl",
                "validation": f"{parent_dataset.s3_prefix}/validation.jsonl",
                "test": f"{parent_dataset.s3_prefix}/test.jsonl",
            }

            bucket, key = utils.split_s3_url(parent_dataset_s3_keys["metadata"])
            parent_dataset_metadata = DatasetMetadata.model_validate_json(
                boto3.client("s3").get_object(Bucket=bucket, Key=key).get("Body").read().decode("utf-8")
            )

            if parent_dataset_metadata.dataset_options.splits != dataset_options.splits:
                raise RuntimeError(
                    f"Parent dataset {dataset_options.parent_dataset_id} is not compatible with requested dataset options"
                )

            # Download the parent dataset files from s3
            if dataset_options.splits:
                parent_dataset_filepaths = {
                    "metadata": f"{data_dir}/datasets/{dataset_options.parent_dataset_id}/metadata.json",
                    "train": f"{data_dir}/datasets/{dataset_options.parent_dataset_id}/train.jsonl",
                    "validation": f"{data_dir}/datasets/{dataset_options.parent_dataset_id}/validation.jsonl",
                    "test": f"{data_dir}/datasets/{dataset_options.parent_dataset_id}/test.jsonl",
                }
            else:
                parent_dataset_filepaths = {
                    "metadata": f"{data_dir}/datasets/{dataset_options.parent_dataset_id}/metadata.json",
                    "datapoints": f"{data_dir}/datasets/{dataset_options.parent_dataset_id}/datapoints.jsonl",
                }

            os.makedirs(os.path.dirname(parent_dataset_filepaths["metadata"]), exist_ok=True)

            for name, filepath in parent_dataset_filepaths.items():
                bucket, key = utils.split_s3_url(parent_dataset_s3_keys[name])
                utils.s3_download_object(bucket, key, filepath)

        else:
            parent_dataset_filepaths = {}
            parent_dataset_s3_keys = {}

    # If a pipeline ID is provided then create the dataset based on the data source and deployment crop IDs found
    # in the pipeline. Otherwise use what is provided in the dataset options.
    crop_ids: Optional[list[str]]
    if dataset_options.pipeline_id is not None:
        pipeline = queries.pipelines.get(pipeline_id=dataset_options.pipeline_id)

        if pipeline is None:
            raise ValueError(f"No pipeline matching pipeline ID: {dataset_options.pipeline_id}")

        crop_ids = [crop.id for crop in pipeline.data_sources + pipeline.deployments]
    else:
        crop_ids = dataset_options.crop_ids

    with perf_tracker.timer("Querying image count"):
        filters = queries.images.ImageFilters(
            captured_at=(dataset_options.start_timestamp, dataset_options.stop_timestamp),
            robot_ids=dataset_options.robot_ids,
            crop_ids=crop_ids,
            geohashes=dataset_options.geohashes,
            excluded_robot_ids=dataset_options.excluded_robot_ids,
            trainable=not dataset_options.unlabeled_data,
        )

        split_selector = SplitSelector(
            dataset_options=dataset_options,
            train_percentage=0.7,
            validation_percentage=0.15,
            test_percentage=0.15,
            parent_dataset_filepaths=parent_dataset_filepaths,
            train_limit=dataset_options.train_limit,
            val_limit=dataset_options.val_limit,
            test_limit=dataset_options.test_limit,
            datapoints_limit=dataset_options.datapoints_limit,
        )

        num_images = queries.images.get_count(filters)

    LOG.info(f"Generating dataset {dataset_id}: found {num_images} images")

    num_pages = math.ceil(num_images / page_size)
    image_index = 0
    progress = 0.0

    if dataset_options.splits:
        splits_counts = {Split.TRAIN: 0, Split.VALIDATION: 0, Split.TEST: 0}
    else:
        splits_counts = {Split.DATAPOINTS: 0}

    cursor: Optional[tables.Image] = None

    files = {}
    for split in splits_counts.keys():
        files[split.value] = open(dataset_filepaths[split.value], "a+")

    try:
        last_log_line = utils.epoch_timestamp_ms()
        for _ in range(num_pages):
            with perf_tracker.timer("Query Images"):
                if cursor is None:
                    images = queries.images.get_list(page=1, page_size=page_size, filters=filters, sort_by="id")
                else:
                    images = queries.images.get_list(
                        cursor=cursor.id, page_size=page_size, filters=filters, sort_by="id"
                    )

            if dataset_options.splits:
                splits: dict[Split, list[DatasetDatapoint]] = {Split.TRAIN: [], Split.VALIDATION: [], Split.TEST: []}
            else:
                splits = {Split.DATAPOINTS: []}

            for image in images:
                with perf_tracker.timer("Construct Datapoint"):
                    datapoint = _construct_datapoint(image)

                with perf_tracker.timer("Split Selector"):
                    assigned_split, is_new = split_selector(datapoint)

                if assigned_split is None:
                    cursor = image
                    image_index += 1
                    continue
                else:
                    datapoint.is_new = is_new

                    splits[assigned_split].append(datapoint)
                    splits_counts[assigned_split] += 1

                current_progress = (image_index + 1) / num_images

                elapsed_time = utils.epoch_timestamp_ms() - last_log_line

                if current_progress - 0.05 > progress or verbose or elapsed_time > 60 * 1000:
                    LOG.debug(f"Current split counts: {splits_counts}")
                    LOG.debug(f"Driptape counts: {split_selector._driptape_counts}")
                    progress = current_progress
                    last_log_line = utils.epoch_timestamp_ms()
                    LOG.info(f"[{dataset_id}] Generating Dataset: {progress * 100:.3f}%")

                image_index += 1
                cursor = image

            with perf_tracker.timer("Writing file"):
                for split in splits.keys():
                    for datapoint in splits[split]:
                        files[split.value].write(datapoint.model_dump_json() + "\n")

            exit = True
            for split in splits.keys():
                if (
                    not split_selector.split_limits.get(split)
                    or splits_counts[split] < split_selector.split_limits[split]
                ):
                    exit = False

            if exit:
                break

    except Exception as e:
        LOG.error(f"Failed to generate dataset: {e}")
        for split in splits.keys():
            files[split.value].close()

        raise e

    for split in splits.keys():
        files[split.value].flush()
        files[split.value].close()

    LOG.info(f"Number of items per split: {split_selector._total_item_counts}")
    LOG.info(f"Number of images with driptape per split: {split_selector._driptape_counts}")
    stop_timestamp = utils.epoch_timestamp_ms()

    LOG.info(f"Dataset item counts: {splits_counts}")

    LOG.info(f"Dataset creation timing details: ")
    perf_tracker.print_times()

    dataset_statistics = DatasetStatistics(
        creation_time=stop_timestamp - start_timestamp,
        num_images=num_images,
        num_pages=num_pages,
        page_size=page_size,
        split_counts={split.value: value for split, value in splits_counts.items()},
    )

    dataset_metadata = DatasetMetadata(
        id=dataset_id,
        name=name,
        created=created,
        version=2,
        num_images=num_images,
        dataset_options=dataset_options,
        statistics=dataset_statistics,
    )

    with open(dataset_filepaths["metadata"], "w") as f:
        f.write(dataset_metadata.model_dump_json(indent=4))

    # Transfer generated files to s3
    for key, filepath in dataset_filepaths.items():
        bucket, key = utils.split_s3_url(dataset_s3_keys[key])
        utils.s3_upload_file(filepath, bucket, key)

    # Cleanup local files
    for filepath in dataset_filepaths.values():
        os.remove(filepath)

    for filepath in parent_dataset_filepaths.values():
        os.remove(filepath)

    # Create dataset in database
    if dataset_options.splits:
        queries.dataset.create(
            id=dataset_id,
            created=created,
            train=dataset_s3_keys["train"],
            validation=dataset_s3_keys["validation"],
            test=dataset_s3_keys["test"],
            s3_prefix=s3_prefix,
            num_images=num_images,
            parent_id=dataset_options.parent_dataset_id,
        )
    else:
        queries.dataset.create(
            id=dataset_id,
            created=created,
            s3_prefix=s3_prefix,
            num_images=num_images,
            parent_id=dataset_options.parent_dataset_id,
        )

    utils.get_redis().hdel("dataset_tasks", dataset_id)
    utils.get_redis().hdel("dataset_tasks", name)
