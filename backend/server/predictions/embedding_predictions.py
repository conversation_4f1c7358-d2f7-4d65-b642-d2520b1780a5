import json
import logging
import traceback
from typing import Optional, Union

import boto3

from server import config, constants, mock, utils
from server.db import queries
from server.predictions.embedding_types import (
    ComparisonEmbeddingRequest,
    ComparisonEmbeddingResponse,
    DeepweedEmbeddingRequest,
    DeepweedEmbeddingResponse,
    EmbeddingPredictionData,
    FewshotEmbeddingRequest,
    FewshotEmbeddingResponse,
)
from server.utils.types import LongRunningJobStatus

VESELKA_CV_COMPUTE_CAPABILITY = constants.ComputeCapability.AMPERE

LOG = logging.getLogger(__name__)

IS_PRODUCTION = config.MODE != constants.MODE_DEVELOPMENT and config.MODE != constants.MODE_ANONYMOUS


class EmbeddingPrediction:
    def __init__(
        self,
        model_id: str,
        image_id: str,
        x: Union[int, float],
        y: Union[int, float],
        interactive: bool = False,
        prediction_point_id: Optional[str] = None,
    ):
        self._model_id = model_id
        self._image_id = image_id
        self._x = int(x)
        self._y = int(y)
        self._interactive = interactive
        self._prediction_point_id = prediction_point_id
        self._redis_client = utils.get_redis()
        self._prediction_key = f"{self._model_id}-{self._image_id}-{self._x}-{self._y}"
        self._redis_status_key = f"prediction-status/{self._prediction_key}"
        self._redis_data_key = f"prediction-cache/{self._prediction_key}"
        self._s3_key = f"predictions/{config.NAMESPACE}/{self._model_id}/{self._image_id}/embedding-prediction-{self._x}-{self._y}.json"
        self._model_type = queries.models.get_type(model_id=self._model_id)
        self._queue_name = (
            config.VESELKA_PREDICT_QUEUE if not self._interactive else config.VESELKA_INTERACTIVE_PREDICT_QUEUE
        )

        status = self._redis_client.get(self._redis_status_key)

        self._data: Optional[EmbeddingPredictionData]

        time_to_live = config.PREDICTION_CACHE_TTL_SECONDS

        if config.MODE == constants.MODE_DEVELOPMENT:
            return None

        if status is None:
            s3_data = utils.get_s3_object_body(config.PREDICTION_BUCKET, self._s3_key)
            if s3_data is None:
                self._data = None
                self._status = LongRunningJobStatus.PENDING
                self._redis_client.set(self._redis_status_key, self._status.value, ex=time_to_live)
                try:
                    self._submit_prediction_task()
                except Exception as e:
                    self._status = LongRunningJobStatus.FAILED
                    self._redis_client.set(self._redis_status_key, self._status.value, ex=time_to_live)

                    LOG.error("Failed to submit prediction data", e)
                    traceback.print_exc()
                    raise e

            else:
                string_data = s3_data.decode("utf-8")
                self._data = EmbeddingPredictionData(**json.loads(string_data))
                self._status = LongRunningJobStatus.SUCCESS
                self._redis_client.set(self._redis_status_key, self._status.value, ex=time_to_live)
                self._redis_client.set(self._redis_data_key, string_data, ex=time_to_live)
        else:
            self._status = LongRunningJobStatus(status)

            if self._status == LongRunningJobStatus.SUCCESS:
                data = self._redis_client.get(self._redis_data_key)
                assert data is not None, f"failed to find prediction data in redis: {self._redis_data_key}"
                self._data = EmbeddingPredictionData(**json.loads(data))
                self._redis_client.expire(self._redis_status_key, time_to_live)
                self._redis_client.expire(self._redis_data_key, time_to_live)
            elif self._status == LongRunningJobStatus.PENDING:
                self._data = None
            elif self._status == LongRunningJobStatus.FAILED:
                LOG.info("Tried to get embedding prediction for failed prediction")
                self._data = None

    @property
    def model_id(self) -> str:
        return self._model_id

    @property
    def image_id(self) -> str:
        return self._image_id

    @property
    def x(self) -> int:
        return self._x

    @property
    def y(self) -> int:
        return self._y

    @property
    def status(self) -> LongRunningJobStatus:
        if config.MODE == constants.MODE_DEVELOPMENT:
            return LongRunningJobStatus.SUCCESS

        self._update_status_and_data()
        return self._status

    @property
    def data(self) -> Optional[EmbeddingPredictionData]:
        if config.MODE == constants.MODE_DEVELOPMENT:
            return EmbeddingPredictionData(
                image_id=self._image_id,
                model_id=self._model_id,
                embedding=[1.0] * 1024,
                x=self._x,
                y=self._y,
                metadata={},
            )

        self._update_status_and_data()
        return self._data

    @data.setter
    def data(self, data: EmbeddingPredictionData) -> None:
        self._data = data

    def _make_prediction_data(
        self, data: Union[FewshotEmbeddingResponse, DeepweedEmbeddingResponse, ComparisonEmbeddingResponse]
    ) -> EmbeddingPredictionData:
        prediction_data = EmbeddingPredictionData(
            image_id=data.image_id,
            model_id=data.model_id,
            ppi=data.ppi if isinstance(data, DeepweedEmbeddingResponse) else None,
            embedding=data.embedding,
            x=data.x,
            y=data.y,
            metadata=data.metadata,
        )

        return prediction_data

    def save_data(
        self, data: Union[DeepweedEmbeddingResponse, FewshotEmbeddingResponse, ComparisonEmbeddingResponse]
    ) -> None:
        if not data.success:
            LOG.info("Veselka CV reported failure for prediction")
            self._status = LongRunningJobStatus.FAILED
            self._redis_client.set(self._redis_status_key, self._status.value, ex=config.PREDICTION_CACHE_TTL_SECONDS)
            return

        if data is not None:
            self._data = self._make_prediction_data(data)

        if self._data is not None:
            try:
                if config.MODE != constants.MODE_DEVELOPMENT and config.MODE != constants.MODE_ANONYMOUS:
                    s3 = boto3.resource("s3")
                else:
                    s3 = mock.boto3.resource("s3")

                bucket = "carbon-ml"
                object = s3.Object(bucket, self._s3_key)
                object.put(Body=self._data.model_dump_json())

                self._redis_client.set(
                    self._redis_data_key, self._data.model_dump_json(), ex=config.PREDICTION_CACHE_TTL_SECONDS
                )

                self._status = LongRunningJobStatus.SUCCESS
            except Exception as e:
                LOG.error("Failed to save prediction data", e)
                traceback.print_exc()
                self._status = LongRunningJobStatus.FAILED

            self._redis_client.set(self._redis_status_key, self._status.value, ex=config.PREDICTION_CACHE_TTL_SECONDS)

    def resubmit(self) -> None:
        self._status = LongRunningJobStatus.PENDING
        self._redis_client.set(self._redis_status_key, self._status.value, ex=config.PREDICTION_CACHE_TTL_SECONDS)
        self._submit_prediction_task()

    def _update_status_and_data(self) -> None:
        if self._status == LongRunningJobStatus.PENDING:
            value = self._redis_client.get(self._redis_status_key)
            assert value is not None, f"failed to find prediction status in redis: {self._redis_status_key}"
            self._status = LongRunningJobStatus(value)

            if self._status == LongRunningJobStatus.SUCCESS:
                data = self._redis_client.get(self._redis_data_key)
                assert data is not None, f"failed to find prediction data in redis: {self._redis_data_key}"
                self._data = EmbeddingPredictionData(**json.loads(data))

    def _submit_prediction_task(self) -> None:
        LOG.info(f"Submitting prediction task for model ID: {self._model_id} and image ID: {self._image_id}")

        model = queries.models.get(model_id=self._model_id)
        model_artifact = queries.model_artifacts.get_by_model_id_compute_capabilty(
            self._model_id, constants.ComputeCapability.AMPERE.value
        )

        assert model_artifact is not None, f"Failed to find model_artifact from model ID: {self._model_id}"

        assert model is not None, f"Failed to find model from model ID: {self._model_id}"

        image = queries.images.get(self._image_id)

        assert image is not None, f"Failed to find image from image ID: {self._image_id}"

        if self._model_type == constants.MODEL_TYPE_DEEPWEED:
            request_dict = DeepweedEmbeddingRequest(
                message_version="v1",
                model_id=self._model_id,
                image_id=self._image_id,
                crop_id=image.crop_id,
                model_s3_path=model_artifact.url,
                image_s3_path=image.url,
                x=self._x,
                y=self._y,
                ppi=image.ppi if image is not None and image.ppi is not None else 200,
                metadata={"prediction_point_id": self._prediction_point_id},
            ).model_dump()
        elif self._model_type == constants.MODEL_TYPE_FEWSHOT or self._model_type == constants.MODEL_TYPE_SELFSUP:
            request_dict = FewshotEmbeddingRequest(
                message_version="v1",
                model_id=self._model_id,
                image_id=self._image_id,
                model_s3_path=model_artifact.url,
                image_s3_path=image.url,
                x=self._x,
                y=self._y,
                metadata={"prediction_point_id": self._prediction_point_id},
            ).model_dump()
        elif self._model_type == constants.MODEL_TYPE_COMPARISON:
            request_dict = ComparisonEmbeddingRequest(
                message_version="v1",
                model_id=self._model_id,
                image_id=self._image_id,
                model_s3_path=model_artifact.url,
                image_s3_path=image.url,
                x=self._x,
                y=self._y,
                metadata={"prediction_point_id": self._prediction_point_id},
            ).model_dump()
        else:
            raise ValueError(f"Unknown model type: {self._model_type}")

        if IS_PRODUCTION:
            sqs = boto3.resource("sqs", endpoint_url="https://sqs.us-west-2.amazonaws.com")
        else:
            sqs = mock.boto3.resource("sqs", endpoint_url="https://sqs.us-west-2.amazonaws.com")

        queue = sqs.get_queue_by_name(QueueName=self._queue_name)

        queue.send_message(
            MessageBody=json.dumps(request_dict),
            MessageAttributes={},
            MessageGroupId="standard",
            MessageDeduplicationId=f"{self._model_id}-{self._image_id}-{self._x}-{self._y}",
        )
