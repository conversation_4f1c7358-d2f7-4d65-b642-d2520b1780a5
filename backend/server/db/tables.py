import datetime
import io
import json
import logging
import os
from typing import Any, Dict, List, Optional, cast

import boto3
from flask_login import UserMixin
from pgvector.sqlalchemy import Vector
from PIL import Image as pImage
from sqlalchemy import (
    ARRAY,
    BigInteger,
    Boolean,
    CheckConstraint,
    Column,
    DateTime,
    Enum,
    Float,
    ForeignKey,
    Index,
    Integer,
    Numeric,
    String,
    UniqueConstraint,
    func,
    text,
)
from sqlalchemy.dialects.postgresql import ARRAY as PG_ARRAY
from sqlalchemy.dialects.postgresql import JSON, JSONB
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import backref, relationship, validates

from server import config, constants
from server.db import Base, join_tables
from server.db.utils import uuid4_str
from server.utils import epoch_timestamp_ms, get_redis, helpers

LOG = logging.getLogger(__name__)


class HasCreatedUpdated:
    created = Column(BigInteger, default=epoch_timestamp_ms)
    updated = Column(BigInteger, default=epoch_timestamp_ms, onupdate=epoch_timestamp_ms)


class User(Base, HasCreatedUpdated, UserMixin):
    __tablename__ = "users"

    id = Column(String, primary_key=True, default=uuid4_str)
    visited = Column(BigInteger, default=epoch_timestamp_ms)
    valid = Column(Integer, default=1)
    admin = Column(Integer, default=0)
    _can_review_tasks = Column("can_review_tasks", Integer, default=0)
    auth0_id = Column(String)

    def __str__(self) -> str:
        return f"User({self.id})"

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "created": self.created,
            "visited": self.visited,
            "valid": self.valid,
            "admin": self.admin,
            "can_review_tasks": self.can_review_tasks,
        }

    @property
    def can_review_tasks(self) -> bool:
        return cast(bool, self.admin or self._can_review_tasks)


class Image(Base, HasCreatedUpdated):
    __tablename__ = "images"
    id = Column(String, primary_key=True, default=uuid4_str)
    location = Column(String, ForeignKey("locations.id", ondelete="CASCADE"), index=True)
    robot_id = Column(String, index=True)
    url = Column(String, index=True)
    height = Column(Integer)
    width = Column(Integer)
    ppi = Column(Integer)
    captured_at = Column(BigInteger, index=True)
    reason_json = Column(String)
    priority = Column(String)
    cam_id = Column(String)
    row_id = Column(String)
    geo_json = Column(String)
    crop = Column(String, index=True)
    image_type = Column(String)
    city = Column(String)
    corrected_hw = Column(Boolean)
    crop_id = Column(String, ForeignKey("crops.id"), index=True)
    focus_metric = Column(Numeric)
    geohash = Column(String(12))
    quarantine_reason = Column(String)
    in_holdout_test_set = Column(Boolean, nullable=False, default=False)
    capture_session_id = Column(String, ForeignKey("capture_sessions.id"))
    checksum = Column(String, nullable=True, default=None)
    content_length = Column(Integer, nullable=True, default=None)

    cv_label = relationship("CVLabel")
    crop_obj = relationship("Crop")
    location_obj = relationship("Location")
    tasks = relationship("Task", secondary=join_tables.TaskImages, back_populates="images")
    predictions = relationship("Prediction")
    capture_session = relationship("CaptureSession")

    # Relationships for the image metadata
    target_image_metadata = relationship(
        "TargetImageMetadata",
        uselist=False,
        primaryjoin="Image.id == foreign(TargetImageMetadata.target_image_id)",
        foreign_keys="[TargetImageMetadata.target_image_id]",
        cascade="all, delete-orphan",
        back_populates="target_image",
    )

    def __str__(self) -> str:
        return f'Image(id="{self.id}",crop_id="{self.crop_id}",location="{self.location}",robot_id="{self.robot_id}",url="{self.url}")'

    def __repr__(self) -> str:
        return self.__str__()

    def to_dict(self, keys: List[str] = []) -> Dict[str, Any]:
        empty_keys: bool = len(keys) == 0
        result: Dict[str, Any] = {}
        for col in self.__table__.columns:
            if empty_keys or col.name in keys:
                if col.name == "location":
                    result[col.name] = self.location_obj.name if self.location_obj else None
                else:
                    result[col.name] = getattr(self, col.name)
        if empty_keys or "categories" in keys:
            result["categories"] = self.location_obj.categories_obj.data if self.location_obj else None
        if empty_keys or "dimensions" in keys:
            result["dimensions"] = self.dimensions
        if empty_keys or "ppcm" in keys:
            result["ppcm"] = self.ppcm

        result["capture_session_name"] = self.capture_session.name if self.capture_session else None
        return result

    @property
    def dimensions(self) -> Dict[str, Any]:
        """
        The height and width was erroneously swapped and lived like this for a long while.
        Now we need to ensure the values have been corrected and return the correct values.
        Eventually we should remove the corrected_hw field and only serve the correct values.
        """
        if self.corrected_hw:
            return {"width": self.width, "height": self.height}

        return {"width": self.height, "height": self.width}

    @property
    def ppcm(self) -> float:
        # inch to cm
        return cast(float, self.ppi / 2.54)

    def get_metadata_dict(self) -> Dict[str, Any]:
        # We only support metadata in s3 for now
        if self.url[0:2] != "s3":
            return {}

        metadata_url = self.url.rsplit(".", 1)[0] + ".metadata.json"

        try:
            s3bucket, s3key = metadata_url[5:].split("/", 1)

            s3 = boto3.resource("s3")
            obj = s3.Object(s3bucket, s3key)

            blob = obj.get()["Body"].read().decode("utf-8")

            resp = json.loads(blob)

        except Exception as e:
            LOG.warning(f"Failed to get image metadata: {e}")
            resp = {}

        return cast(Dict[str, Any], resp)

    # Can't call this metadata - sqlalchemy uses that prop
    @property
    def metadata_dict(self) -> Dict[str, Any]:
        if getattr(self, "_metadata_dict", None) is not None:
            return cast(Dict[str, Any], getattr(self, "_metadata_dict"))
        metadata = self.get_metadata_dict()
        setattr(self, "_metadata_dict", metadata)
        return metadata

    def get_cache_jpg(self) -> Optional[str]:
        if not self.url.startswith("s3://"):
            LOG.warning(f"unsupported image url:{self.url}")
            return None

        bucket, key = helpers.split_s3_url(self.url)
        cached_file = f"{config.TMP_IMAGE_DIR}/{self.id}.jpg"
        get_redis().setex(f"file_lock_{cached_file}", 3600, "true")
        if not os.path.exists(cached_file) or not self.id:
            LOG.debug(f"{cached_file} missing, retrieving")
            img_data = helpers.get_s3_object_body(bucket, key)
            if img_data is None:
                return None
            img = pImage.open(io.BytesIO(img_data))
            img.save(cached_file, "jpeg", quality=95)

        return cached_file

    @property
    def crop_name(self) -> str:
        if self.crop_obj:
            return cast(str, self.crop_obj.common_name)
        if self.crop:
            return cast(str, self.crop)
        return ""

    @hybrid_property
    def quarantined(self) -> bool:
        return self.quarantine_reason is not None

    @quarantined.expression  # type: ignore[no-redef]
    def quarantined(cls):
        return cls.quarantine_reason.isnot(None)

    @property
    def date(self) -> datetime.date:
        return datetime.date.fromtimestamp(self.captured_at / 1000)


class TargetImageMetadata(Base, HasCreatedUpdated):
    __tablename__ = "target_image_metadata"

    id = Column(String, primary_key=True, default=uuid4_str)
    target_image_id = Column(String, ForeignKey("images.id", ondelete="CASCADE"), index=True, unique=True)
    context_image_id = Column(String, ForeignKey("images.id", ondelete="CASCADE"))

    target_image = relationship(
        "Image",
        foreign_keys=[target_image_id],
        back_populates="target_image_metadata",
    )
    context_image = relationship("Image", foreign_keys=[context_image_id], viewonly=True)


class Question(Base, HasCreatedUpdated):
    __tablename__ = "questions"

    id = Column(String, primary_key=True, default=uuid4_str)
    question = Column(JSONB, default=None)
    filters = Column(JSONB, default=lambda: {})
    data_type = Column(String)
    valid = Column(Integer, default=1)

    def to_dict(self, keys: Optional[List[str]] = None, get_joins: bool = False) -> Dict[str, Any]:
        dict_trigger = {col.name: getattr(self, col.name) for col in self.__table__.columns}
        if keys is None:
            result = dict_trigger
        else:
            result = {key: dict_trigger[key] for key in keys}
        return result

    def should_classify(self, image: Image) -> bool:
        image_filters = self.filters.get("images", {})
        for filter, value in image_filters.items():
            if getattr(image, filter) != value:
                return False

        return True


class Answer(Base, HasCreatedUpdated):
    __tablename__ = "answers"

    id = Column(String, primary_key=True, default=uuid4_str)
    question_task_id = Column(String, ForeignKey("question_tasks.id"))
    answer = Column(JSONB, default=None)
    user_id = Column(String, ForeignKey("users.id"))
    valid = Column(Integer, default=1)

    question_task = relationship(
        "QuestionTask",
        uselist=False,
        lazy="joined",
        backref=backref(
            "answers", primaryjoin=("and_(QuestionTask.id == Answer.question_task_id, Answer.valid == 1)"), uselist=True
        ),
    )

    def to_dict(self, keys: Optional[List[str]] = None, get_joins: bool = False) -> Dict[str, Any]:
        dict_trigger = {col.name: getattr(self, col.name) for col in self.__table__.columns}
        if keys is None:
            result = dict_trigger
        else:
            result = {key: dict_trigger[key] for key in keys}
        return result


class QuestionTask(Base, HasCreatedUpdated):
    __tablename__ = "question_tasks"
    id = Column(String, primary_key=True, default=uuid4_str)
    image_id = Column(String, ForeignKey("images.id"))
    question_id = Column(String, ForeignKey("questions.id"))
    done = Column(BigInteger, default=0)
    user_id = Column(String, ForeignKey("users.id"))
    valid = Column(Integer, default=1)
    reserved = Column(BigInteger, default=0)

    user = relationship(User, uselist=False)
    image = relationship("Image", uselist=False, backref="question_tasks")
    question = relationship(Question, uselist=False, lazy="joined", backref=backref("tasks", lazy="dynamic"))

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "image_id": self.image_id,
            "question": self.question.to_dict(),
            "user_id": self.user_id,
            "answers": self.answers,
        }


class Pipeline(Base, HasCreatedUpdated):
    __tablename__ = "pipelines"
    id = Column(String, primary_key=True, default=uuid4_str)
    name = Column(String(200), nullable=False)
    description = Column(String(1000))
    custom_arguments = Column(JSONB, default=None)
    enabled = Column(Boolean, default=True)
    enable_recommendation = Column(Boolean, default=True, server_default="True")

    models = relationship("Model", backref="models")
    data_sources = relationship("Crop", secondary=join_tables.CropPipelineDataSource, back_populates="data_sources")
    deployments = relationship("Crop", secondary=join_tables.CropPipelineDeployment, back_populates="deployments")
    job_types = relationship("JobType", secondary=join_tables.PipelinesJobTypes, back_populates="pipelines")

    def __str__(self) -> str:
        return str(self.to_dict())

    def to_dict(self, keys: Optional[List[str]] = None, get_joins: bool = False) -> Dict[str, Any]:
        dict_trigger = {col.name: getattr(self, col.name) for col in self.__table__.columns}
        if keys is None:
            result = dict_trigger
        else:
            result = {key: dict_trigger[key] for key in keys}

        if get_joins:
            result["crop_ids"] = self.crop_ids
            result["data_sources_crop_ids"] = self.data_sources_crop_ids
            result["deployments_crop_ids"] = self.deployments_crop_ids
            result["job_type_ids"] = self.job_type_ids

        return result

    @property
    def crop_ids(self) -> List[str]:
        return list(set(self.data_sources_crop_ids + self.deployments_crop_ids))

    @property
    def data_sources_crop_ids(self) -> List[str]:
        return [crop.id for crop in self.data_sources]

    @property
    def deployments_crop_ids(self) -> List[str]:
        return [crop.id for crop in self.deployments]

    @property
    def job_type_ids(self) -> List[str]:
        return [job_type.id for job_type in self.job_types]


class Crop(Base, HasCreatedUpdated):
    __tablename__ = "crops"
    id = Column(String, primary_key=True, default=uuid4_str)
    carbon_name = Column(String(200), nullable=False, unique=True)
    common_name = Column(String(200), nullable=False, unique=True)
    description = Column(String(1000))
    labeling_instructions_url = Column(String)
    legacy_crop_name = Column(String(200))
    archived = Column(Boolean, default=False, nullable=False)
    pinned_model_id = Column(String, ForeignKey("models.id"))
    configuration = Column(JSON, nullable=False, default={})
    capture_only = Column(Boolean, default=False)
    crop_point_category_id = Column(String, ForeignKey("point_categories.id"))

    data_sources = relationship("Pipeline", secondary=join_tables.CropPipelineDataSource, back_populates="data_sources")
    deployments = relationship("Pipeline", secondary=join_tables.CropPipelineDeployment, back_populates="deployments")
    models = relationship("Model", secondary=join_tables.ModelCrop, back_populates="crops")
    pinned_model = relationship("Model")
    translations = relationship("CropTranslation")
    point_categories = relationship("PointCategory", secondary=join_tables.CropPointCategories, back_populates="crops")
    crop_point_category = relationship("PointCategory")

    def __str__(self) -> str:
        return str(self.to_dict())

    def to_dict(self, keys: Optional[List[str]] = None) -> Dict[str, Any]:
        result: Dict[str, Any] = {}
        for col in self.__table__.columns:
            if keys is None or "*" in keys or col.name in keys:
                result[col.name] = getattr(self, col.name)

        if keys is None:
            return result

        # not included by default
        if "translation_ids" in keys:
            result["translation_ids"] = self.translation_ids
        if "pipeline_ids" in keys:
            result["pipeline_ids"] = self.pipeline_ids
        if "data_sources_pipeline_ids" in keys:
            result["data_sources_pipeline_ids"] = self.data_sources_pipeline_ids
        if "deployments_pipeline_ids" in keys:
            result["deployments_pipeline_ids"] = self.deployments_pipeline_ids

        return result

    @property
    def pipeline_ids(self) -> List[str]:
        return list(set(self.data_sources_pipeline_ids + self.deployments_pipeline_ids))

    @property
    def data_sources_pipeline_ids(self) -> List[str]:
        return [pipeline.id for pipeline in self.data_sources]

    @property
    def deployments_pipeline_ids(self) -> List[str]:
        return [pipeline.id for pipeline in self.deployments]

    @property
    def translation_ids(self) -> List[str]:
        return [translation.id for translation in self.translations]

    @validates("carbon_name")  # type: ignore
    def validate_carbon_name(self, key, carbon_name: str) -> str:
        if not carbon_name or len(carbon_name) > 100:
            raise ValueError("carbon_name must be between 1 and 100 characters")
        return carbon_name

    @validates("common_name")  # type: ignore
    def validate_common_name(self, key, common_name: str) -> str:
        if not common_name or len(common_name) > 100:
            raise ValueError("common_name must be between 1 and 100 characters")
        return common_name


class CropTranslation(Base, HasCreatedUpdated):
    __tablename__ = "crop_translations"
    id = Column(String, primary_key=True, default=uuid4_str)
    name = Column(String, nullable=False)
    language = Column(String, nullable=False)
    version = Column(Integer, nullable=False)
    crop_id = Column(String, ForeignKey("crops.id"), nullable=False)

    crop_id_language_version_constraint = UniqueConstraint(
        crop_id, language, version, name="crop_translations_crop_id_language_version_key"
    )

    def to_dict(self, keys: Optional[List[str]] = None) -> Dict[str, Any]:
        result: Dict[str, Any] = {}
        for col in self.__table__.columns:
            if keys is None or col.name in keys:
                result[col.name] = getattr(self, col.name)
        return result


class CropEnvironment(Base):
    __tablename__ = "crop_environments"
    id = Column(String, primary_key=True, default=uuid4_str)
    crop = Column(String, nullable=False)
    environment = Column(String, nullable=False)


class Dataset(Base, HasCreatedUpdated):
    __tablename__ = "datasets"
    id = Column(String, primary_key=True, default=uuid4_str)
    name = Column(String)
    train = Column(String)
    validation = Column(String)
    test = Column(String)
    s3_prefix = Column(String)  # Used for dataset v2 when there are no split files and only a single dataset file
    parent_id = Column(String)
    num_images = Column(Integer)
    fast_run = Column(Boolean)
    type = Column(String)

    capture_sessions = relationship(
        "CaptureSession", secondary=join_tables.CaptureSessionDatasets, back_populates="datasets"
    )

    def __str__(self) -> str:
        return str(self.to_dict())

    def to_dict(self) -> Dict[str, Any]:
        representation = {
            "id": self.id,
            "name": self.name,
            "created": self.created,
            "train": self.train,
            "validation": self.validation,
            "test": self.test,
            "parent_id": self.parent_id,
            "num_images": self.num_images,
            "fast_run": self.fast_run,
            "s3_prefix": self.s3_prefix,
        }
        return representation


class DatasetLabel(Base):
    __tablename__ = "dataset_labels"
    id = Column(String, primary_key=True, default=uuid4_str)
    dataset_id = Column(String, ForeignKey("datasets.id"), nullable=False, index=True)
    label_id = Column(String, ForeignKey("labels.id"), nullable=False, index=True)
    role = Column(String, nullable=False)

    dataset = relationship("Dataset")
    label = relationship("Label")

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "dataset_id": self.dataset_id,
            "label_id": self.label_id,
            "role": self.role,
        }


class PointDataset(Base, HasCreatedUpdated):
    __tablename__ = "point_datasets"
    id = Column(String, primary_key=True, default=uuid4_str)
    name = Column(String, nullable=False)
    owner_user_id = Column(String, ForeignKey("users.id"), nullable=True)
    is_private = Column(Boolean, nullable=False)

    owner_user = relationship("User", foreign_keys=[owner_user_id])
    categories = relationship("PointDatasetCategory", back_populates="point_dataset")

    def to_dict(self, keys: Optional[List[str]] = None) -> Dict[str, Any]:
        result: Dict[str, Any] = {}
        for col in self.__table__.columns:
            if keys is None or col.name in keys:
                result[col.name] = getattr(self, col.name)
        return result


class PointDatasetCategory(Base, HasCreatedUpdated):
    __tablename__ = "point_dataset_categories"
    id = Column(String, primary_key=True, default=uuid4_str)
    name = Column(String, nullable=False)
    color_hex = Column(String, nullable=False)
    point_dataset_id = Column(String, ForeignKey("point_datasets.id"), nullable=False)

    unique_name_per_point_dataset_constraint = UniqueConstraint(
        name, point_dataset_id, name="point_dataset_category_name_constraint"
    )
    valid_color_hex_constraint = (
        CheckConstraint(
            f"color_hex ~ '{helpers.COLOR_HEX_REGEX}'", name="valid_color_hex_constraint"  # Regex for valid hex codes
        ),
    )

    point_dataset = relationship("PointDataset", back_populates="categories")
    label_points = relationship("LabelPoint", secondary=join_tables.LabelPointPointDatasetCategories)

    def to_dict(self, keys: Optional[List[str]] = None) -> Dict[str, Any]:
        result: Dict[str, Any] = {}
        for col in self.__table__.columns:
            if keys is None or col.name in keys:
                result[col.name] = getattr(self, col.name)
        return result


class ModelRatioMetric(Base, HasCreatedUpdated):
    __tablename__ = "model_ratio_metrics"
    id = Column(String, primary_key=True, default=uuid4_str)
    image_id = Column(String, ForeignKey("images.id"), nullable=False)
    model_id = Column(String, ForeignKey("models.id"), nullable=False)
    label_id = Column(String, ForeignKey("labels.id"), nullable=True)
    weed_point_threshold = Column(Float, nullable=False)
    crop_point_threshold = Column(Float, nullable=False)
    hit_distance = Column(Float, nullable=False)
    crop_protection_radius = Column(Float, nullable=False)
    metric_name = Column(String, nullable=False)
    numerator = Column(Integer, nullable=False)
    denominator = Column(Integer, nullable=False)
    geohash = Column(String(12))
    historical = Column(Boolean, nullable=True)

    model = relationship("Model")

    geohash_index = Index("ix_model_ratio_metrics_geohash_v1", geohash)
    historical_index = Index("ix_model_ratio_metrics_historical_v1", historical)
    model_id_image_id_index = Index("ix_model_ratio_metrics_model_id_image_id_v1", model_id, image_id)
    model_id_index = Index("ix_model_ratio_metrics_model_id_v3", model_id)


class ModelRecommendationHistory(Base, HasCreatedUpdated):
    __tablename__ = "model_recommendation_history"
    id = Column(String, primary_key=True, default=uuid4_str)
    robot_id = Column(String, nullable=False)
    model_id = Column(String, ForeignKey("models.id"))
    crop_id = Column(String, ForeignKey("crops.id"))
    requested_at = Column(BigInteger, default=epoch_timestamp_ms)
    parameters_json = Column(JSONB, default=lambda: {})
    results_json = Column(JSONB, default=None)
    considered_model_ids = Column(JSONB, default=lambda: [])

    robot_crop_index = Index("ix_model_recommendation_history_robot_crop", robot_id, crop_id)

    def __repr__(self) -> str:
        return str(self.to_dict())

    def to_dict(self) -> Dict[str, Any]:
        return {
            **{col.name: getattr(self, col.name) for col in self.__table__.columns},
        }


class PriorityOverride(Base):
    __tablename__ = "priority_overrides"

    id = Column(String, primary_key=True, default=uuid4_str)
    created = Column(DateTime, server_default=func.now())
    valid_start = Column(DateTime)
    valid_end = Column(DateTime)
    overrides = Column(JSON, default=None)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "valid_start": self.valid_start.isoformat() if self.valid_start else None,
            "valid_end": self.valid_end.isoformat() if self.valid_end else None,
            "params": self.overrides[0],
            "multiple": self.overrides[1],
        }


class Evaluations(Base, HasCreatedUpdated):
    __tablename__ = "evaluations"
    id = Column(String, primary_key=True, default=uuid4_str)
    model_id = Column(String, ForeignKey("models.id"), nullable=False)
    test_oec = Column(Float, nullable=False)
    deployable = Column(Boolean, nullable=False)
    dataset_id = Column(String, ForeignKey("datasets.id"))

    def to_dict(self) -> Dict[str, Any]:
        return {col.name: getattr(self, col.name) for col in self.__table__.columns}


class Label(Base, HasCreatedUpdated):
    WORKFLOW_CLASSIFICATION = "classification"
    WORKFLOW_LABELING = "labeling"
    WORKFLOW_REVIEW = "review"
    WORKFLOW_ADMIN = "admin"

    __tablename__ = "labels"

    id = Column(String, primary_key=True, default=uuid4_str)
    task = Column(String, ForeignKey("tasks.id", ondelete="CASCADE"), index=True)
    user = Column(String, ForeignKey("users.id", ondelete="CASCADE"), index=True)
    original = Column(String, ForeignKey("labels.id"))
    workflow = Column(String)
    valid = Column(Integer, default=1)
    done = Column(BigInteger, default=0)
    data = Column(JSONB, default=None)
    categories = Column(String, ForeignKey("categories.id", ondelete="CASCADE"), index=True)
    polygons = Column(Integer, default=0)
    points = Column(Integer, default=0, index=True)
    weed_points = Column(Integer, default=0, index=True)
    crop_points = Column(Integer, default=0, index=True)
    is_production = Column(Boolean, default=True, server_default="True", nullable=False)
    reject_user_id = Column(String, ForeignKey("users.id"), nullable=True)
    aggregate_data = Column(JSONB, default=lambda: {})
    batch_prediction = Column(String, ForeignKey("batch_predictions.id"))
    model_id = Column(String)
    label_points_populated = Column(Boolean, default=False, server_default="False", nullable=False)

    label = relationship("Label")
    user_obj = relationship("User", foreign_keys=[user])
    reject_user = relationship("User", foreign_keys=[reject_user_id])
    categories_obj = relationship("Categories")
    tsk = relationship("Task")
    original_label = relationship("Label", remote_side=[id])
    point_objs = relationship("LabelPoint")

    def __str__(self) -> str:
        return f"Label(id={self.id},task={self.task},user={self.user},model_id={self.model_id},original={self.original},workflow={self.workflow},created={self.created},updated={self.updated},done={self.done},valid={self.valid})"

    def __repr__(self) -> str:
        return self.__str__()

    @property
    def labeled_by_model(self) -> bool:
        if self.model_id:
            return True
        if self.original_label:
            return bool(self.original_label.model_id)
        return False

    def to_dict(self, keys: List[str] = []) -> Dict[str, Any]:
        empty_keys: bool = len(keys) == 0
        result: Dict[str, Any] = {}
        data = self.data
        if not isinstance(data, dict):
            data = {}

        for col in self.__table__.columns:
            if empty_keys or col.name in keys:
                if col.name == "points":  # TODO:(smt) these really shouldn't be overloaded here
                    result["points"] = data.get("points", [])
                elif col.name == "polygons":
                    result["polygons"] = data.get("polygons", [])
                else:
                    result[col.name] = getattr(self, col.name)

        if empty_keys or "labeled_by_model" in keys:
            result["labeled_by_model"] = self.labeled_by_model
        if empty_keys or "splines" in keys:
            result["splines"] = data.get("splines", [])
        return result


class Task(Base, HasCreatedUpdated):
    __tablename__ = "tasks"

    CROWNS_IMAGE_ZOOM = 0.75
    POLYGONS_IMAGE_ZOOM = 0.66
    # ~ 4 hours
    RESERVATION_TIME = 15000000

    NUM_LABELS_UNLABELED = 0
    NUM_LABELS_LABELING = 1
    NUM_LABELS_REVIEW = 2

    id = Column(String, primary_key=True, default=uuid4_str)
    user = Column(String, ForeignKey("users.id", ondelete="CASCADE"), index=True)
    priority = Column(Integer, default=0)
    reserved = Column(BigInteger, default=0)
    valid = Column(Integer, default=1, index=True)
    numlabels = Column(Integer, default=0, index=True)
    invalidated_by_id = Column(String, ForeignKey("users.id"))
    invalidated_reason = Column(Integer)

    images = relationship("Image", secondary=join_tables.TaskImages, back_populates="tasks")
    labels = relationship("Label", foreign_keys=[Label.task], back_populates="tsk")
    user_obj = relationship("User", foreign_keys=[user])
    invalidated_by = relationship("User", foreign_keys=[invalidated_by_id])

    def __str__(self) -> str:
        return f"Task(id={self.id}, created={self.created}, updated={self.updated}, user={self.user}, priority={self.priority}, reserved={self.reserved}, valid={self.valid}, numlabels={self.numlabels}, invalidated_reason={self.invalidated_reason})"

    def __repr__(self) -> str:
        return self.__str__()

    def to_dict(self, keys: List[str] = []) -> Dict[str, Any]:
        empty_keys: bool = len(keys) == 0
        result: Dict[str, Any] = {}
        for col in self.__table__.columns:
            if empty_keys or col.name in keys:
                result[col.name] = getattr(self, col.name)

        if empty_keys or "zoom" in keys:
            result["zoom"] = 0.75
        if empty_keys or "image_id" in keys:
            result["image_id"] = self.image.id

        return result

    @property
    def question_tasks(self) -> List[QuestionTask]:
        return cast(List[QuestionTask], self.image.question_tasks)

    @property
    def unanswered_question_tasks(self) -> List[QuestionTask]:
        return [question for question in self.image.question_tasks if not question.answers]

    @property
    def image(self) -> Image:
        return cast(Image, self.images[0])

    def image_to_dict(self) -> Dict[str, Any]:
        return {"imageId": self.image.id}


class CVLabel(Base, HasCreatedUpdated):
    __tablename__ = "cv_labels"

    id = Column(String, primary_key=True, default=uuid4_str)
    image = Column(String, ForeignKey("images.id", ondelete="CASCADE"), index=True)
    batch_prediction = Column(String, ForeignKey("batch_predictions.id", ondelete="CASCADE"), index=True)
    comment = Column(String)
    data = Column(JSON, default=lambda: {})
    categories = Column(String, ForeignKey("categories.id", ondelete="CASCADE"), index=True)
    polygons = Column(Integer, default=0)
    points = Column(Integer, default=0)
    _evaluations = Column("evaluations", String, default="{}")

    bp = relationship("BatchPrediction")

    @property
    def evaluations(self) -> Dict[str, Any]:
        return cast(Dict[str, Any], json.loads(str(self._evaluations) or "{}"))

    def set_evaluation(self, name: str, evaluation: float) -> None:
        evals = self.evaluations
        evals[name] = evaluation
        self._evaluations = json.dumps(evals)

    def to_dict(self) -> Dict[str, Any]:
        points = self.data.get("points", [])
        polygons = self.data.get("polygons", [])

        return {
            "id": self.id,
            "points": points,
            "polygons": polygons,
            "model_id": self.bp.model_id,
            "model_threshold": self.bp.model_threshold,
            "crop_threshold": self.bp.crop_threshold,
            "weed_threshold": self.bp.weed_threshold,
            "segmentation_threshold": self.bp.segmentation_threshold,
            "batch_prediction": self.bp.id,
            "evaluations": self.evaluations,
        }


class Tag(Base, HasCreatedUpdated):
    __tablename__ = "tags"
    id = Column(String, primary_key=True, default=uuid4_str)
    name = Column(String, nullable=False, unique=True)

    predictions = relationship("Prediction", secondary=join_tables.PredictionTag, back_populates="tags")

    def __repr__(self) -> str:
        return f"Tag: name={self.name}"

    def to_dict(self) -> Dict[str, Any]:
        return {col.name: getattr(self, col.name) for col in self.__table__.columns}


class Prediction(Base, HasCreatedUpdated):
    __tablename__ = "predictions"
    id = Column(String, primary_key=True, default=uuid4_str)
    image_id = Column(String, ForeignKey("images.id"), nullable=False, index=True)
    model_id = Column(String, ForeignKey("models.id"), nullable=False)
    weed_point_threshold = Column(Float, nullable=False)
    crop_point_threshold = Column(Float, nullable=False)
    segmentation_threshold = Column(Float, nullable=False)
    data = Column(JSON, nullable=False)
    done = Column(Boolean, nullable=False)

    tags = relationship("Tag", secondary=join_tables.PredictionTag, back_populates="predictions")
    image = relationship("Image")
    points = relationship("PredictionPoint", back_populates="prediction")

    def __repr__(self) -> str:
        return f"Prediction: id={self.id}, image_id={self.image_id}, model_id={self.model_id}"

    def to_dict(self) -> Dict[str, Any]:
        return {col.name: getattr(self, col.name) for col in self.__table__.columns}


class BatchPrediction(Base, HasCreatedUpdated):
    __tablename__ = "batch_predictions"
    id = Column(String, primary_key=True, default=uuid4_str)
    name = Column(String)
    model_id = Column(String)
    model_threshold = Column(Float)
    crop_threshold = Column(Float)
    weed_threshold = Column(Float)
    segmentation_threshold = Column(Float)

    location_id = Column(String, ForeignKey("locations.id", ondelete="CASCADE"), index=True)
    category = Column(String)
    numlabels = Column(Integer)
    crop = Column(String)
    image_url = Column(String)
    filters = Column(JSON, default=lambda: {})

    confidence_threshold = Column(Float)
    prelabel_criteria = Column(JSON, default=lambda: {})

    cv_labels = relationship("CVLabel")
    locations = relationship("Location")

    def __repr__(self) -> str:
        return f"Batch Prediction name={self.name}"

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "name": self.name,
            "created": self.created,
            "model_id": self.model_id,
            "model_threshold": self.model_threshold or min(self.crop_threshold, self.weed_threshold),
            "crop_threshold": self.weed_threshold,
            "weed_threshold": self.crop_threshold,
            "segmentation_threshold": self.segmentation_threshold,
            "location_id": self.location_id,
            "category_id": self.category,
            "numlabels": self.numlabels,
            "crop": self.crop,
            "image_url": self.image_url,
            "filters": self.filters,
        }


class Model(Base, HasCreatedUpdated):
    __tablename__ = "models"
    id = Column(String, primary_key=True, default=uuid4_str)
    url = Column(String)
    crop = Column(String)
    environment = Column(String)
    version = Column(Integer)
    git_sha = Column(String)
    checksum = Column(String)
    location = Column(String)
    trained_at = Column(BigInteger)
    type = Column(String)
    description = Column(String)
    metadata_json = Column(String)
    test_results_json = Column(JSONB, default=None)
    wandb_json = Column(String)
    wandb_url = Column(String)
    is_stub = Column(Boolean)
    deploy = Column(Boolean, default=False)
    sub_type = Column(String)
    dataset_id = Column(String, ForeignKey("datasets.id"))
    container_version = Column(String)
    container_id = Column(String)
    pipeline_id = Column(String, ForeignKey("pipelines.id"))
    parent_model_id = Column(String, ForeignKey("models.id"))
    geohash_prefix = Column(String)
    fast_run = Column(Boolean)
    dl_config = Column(JSONB)
    embedding_version = Column(Integer, nullable=True)

    evaluations = relationship("Evaluations")
    dataset = relationship(Dataset)
    pipeline = relationship(Pipeline)
    crops = relationship("Crop", secondary=join_tables.ModelCrop, back_populates="models")
    deepweed_metadata = relationship("DeepweedModelMetadata")
    model_artifacts = relationship("ModelArtifact")

    def __repr__(self) -> str:
        column_str = ", ".join([f"{column.key}={getattr(self, column.key)}" for column in self.__table__.columns])
        return f"Model({column_str})"

    def to_dict(self, keys: Optional[List[str]] = None) -> Dict[str, Any]:
        result: Dict[str, Any] = {}
        for col in self.__table__.columns:
            if keys is None or col.name in keys:
                result[col.name] = getattr(self, col.name)
        return result

    @property
    def crop_ids(self) -> List[str]:
        return cast(List[str], [crop.id for crop in self.crops])


class ModelArtifact(Base, HasCreatedUpdated):
    __tablename__ = "model_artifacts"
    id = Column(String, primary_key=True, default=uuid4_str)
    model_id = Column(String, ForeignKey("models.id"), nullable=False)
    tensorrt_version = Column(String, nullable=False)
    compute_capability = Column(String, nullable=False)
    url = Column(String)
    checksum = Column(String)
    is_stub = Column(Boolean, nullable=False)
    test_results_json = Column(JSONB)

    model_id_tensorrt_version_compute_capability_constraint = UniqueConstraint(
        model_id,
        tensorrt_version,
        compute_capability,
        name="model_artifacts_model_id_tensorrt_version_compute_capability",
    )

    model = relationship("Model")

    def to_dict(self, include_json_fields: bool = False) -> Dict[str, Any]:
        return {
            col.name: getattr(self, col.name)
            for col in self.__table__.columns
            if include_json_fields or "json" not in col.name
        }


class Location(Base, HasCreatedUpdated):
    __tablename__ = "locations"
    id = Column(String, primary_key=True, default=uuid4_str)
    name = Column(String)
    categories = Column(String, ForeignKey("categories.id", ondelete="CASCADE"), index=True)
    admin = Column(Integer, default=0)

    image = relationship("Image")
    batch_pred = relationship("BatchPrediction")
    categories_obj = relationship("Categories")

    def __repr__(self) -> str:
        return f"Location id={self.id}, name={self.name}, created={self.created}, updated={self.updated}, categories={self.categories}, admin={self.admin}"


class Categories(Base, HasCreatedUpdated):
    __tablename__ = "categories"
    id = Column(String, primary_key=True, default=uuid4_str)
    data = Column(String, nullable=False)

    location = relationship("Location")
    cv_label = relationship("CVLabel")
    label = relationship("Label")


class JobType(Base, HasCreatedUpdated):
    __tablename__ = "job_types"
    id = Column(String, primary_key=True, default=uuid4_str)
    name = Column(String, nullable=False)
    prefix = Column(String, nullable=False)
    command = Column(String, nullable=False)
    failure_command = Column(String, nullable=False)
    retries = Column(BigInteger, nullable=False, default=5)
    node_group = Column(String, nullable=False)
    priority = Column(Integer, nullable=False)
    enabled = Column(Boolean, nullable=False, default=True)
    max_runtime_seconds = Column(Integer, nullable=False, default=28800)
    dl_config = Column(JSONB, nullable=False)
    pretrained_model = Column(Boolean, nullable=False, default=False)
    data_limit = Column(Boolean, nullable=False, default=True)
    data_source_emphasized_limit = Column(Boolean, nullable=False, default=False)
    deployment_emphasized_limit = Column(Boolean, nullable=False, default=False)
    data_source_unemphasized_limit = Column(Boolean, nullable=False, default=False)
    deployment_unemphasized_limit = Column(Boolean, nullable=False, default=False)
    link_on_pipeline_creation = Column(Boolean, nullable=False, default=False)
    gpu_type = Column(String, nullable=False, default=constants.GPUTypes.NVIDIA_RTX_A5000.value)
    nfs_mount = Column(String, nullable=True, default=None)

    triggers = relationship("Trigger", secondary=join_tables.JobTypesTriggers, back_populates="job_types")
    pipelines = relationship("Pipeline", secondary=join_tables.PipelinesJobTypes, back_populates="job_types")

    def to_dict(self, keys: Optional[List[str]] = None, get_joins: bool = False) -> Dict[str, Any]:
        dict_trigger = {col.name: getattr(self, col.name) for col in self.__table__.columns}
        if keys is None:
            result = dict_trigger
        else:
            result = {key: dict_trigger[key] for key in keys}

        if get_joins:
            result["pipeline_ids"] = self.pipeline_ids
            result["trigger_ids"] = self.trigger_ids

        return result

    @property
    def pipeline_ids(self) -> List[str]:
        return [pipeline.id for pipeline in self.pipelines]

    @property
    def trigger_ids(self) -> List[str]:
        return [trigger.id for trigger in self.triggers]


class Trigger(Base, HasCreatedUpdated):
    __tablename__ = "triggers"
    id = Column(String, primary_key=True, default=uuid4_str)
    name = Column(String, nullable=False)
    type = Column(String, nullable=False)
    enabled = Column(Boolean, nullable=False, default=True)
    parameters = Column(String, nullable=True, default="")
    production = Column(Boolean, nullable=False, default=True)
    preview = Column(Boolean, nullable=False, default=False)
    development = Column(Boolean, nullable=False, default=False)
    service = Column(String, nullable=False, default=constants.TriggerService.JOB_CREATOR)

    job_types = relationship("JobType", secondary=join_tables.JobTypesTriggers, back_populates="triggers")

    def to_dict(self, keys: Optional[List[str]] = None, get_joins: bool = False) -> Dict[str, Any]:
        dict_trigger = {col.name: getattr(self, col.name) for col in self.__table__.columns}
        if keys is None:
            result = dict_trigger
        else:
            result = {key: dict_trigger[key] for key in keys}

        if get_joins:
            result["job_type_ids"] = self.job_type_ids

        return result

    @property
    def job_type_ids(self) -> List[str]:
        return [job_type.id for job_type in self.job_types]


class PointCategory(Base, HasCreatedUpdated):
    __tablename__ = "point_categories"
    id = Column(String, primary_key=True, default=uuid4_str)
    name = Column(String, nullable=False)
    display_name = Column(String, nullable=False)
    description = Column(String, nullable=False)
    translation_context = Column(String, nullable=True)
    archived = Column(Boolean, nullable=False, default=True)
    translations = relationship("PointCategoryTranslation")
    crops = relationship("Crop", secondary=join_tables.CropPointCategories, back_populates="point_categories")

    def to_dict(self, keys: Optional[List[str]] = None) -> Dict[str, Any]:
        result: Dict[str, Any] = {}
        for col in self.__table__.columns:
            if keys is None or "*" in keys or col.name in keys:
                result[col.name] = getattr(self, col.name)
        if keys is None:
            return result
        # not included by default
        if "translation_ids" in keys:
            result["translation_ids"] = self.translation_ids
        return result

    @property
    def translation_ids(self) -> List[str]:
        return [translation.id for translation in self.translations]


class PointCategoryTranslation(Base, HasCreatedUpdated):
    __tablename__ = "point_category_translations"
    id = Column(String, primary_key=True, default=uuid4_str)
    name = Column(String, nullable=False)
    language = Column(String, nullable=False)
    version = Column(Integer, nullable=False)
    point_category_id = Column(String, ForeignKey("point_categories.id"), nullable=False)
    point_category_id_language_version_constraint = UniqueConstraint(
        point_category_id, language, version, name="point_category_tr_point_categories_id_language_version_key"
    )

    def to_dict(self, keys: Optional[List[str]] = None) -> Dict[str, Any]:
        result: Dict[str, Any] = {}
        for col in self.__table__.columns:
            if keys is None or "*" in keys or col.name in keys:
                result[col.name] = getattr(self, col.name)
        return result


class CaptureSession(Base, HasCreatedUpdated):
    __tablename__ = "capture_sessions"
    id = Column(String, primary_key=True, default=uuid4_str)
    name = Column(String, unique=True, nullable=False)

    datasets = relationship("Dataset", secondary=join_tables.CaptureSessionDatasets, back_populates="capture_sessions")
    images = relationship("Image")

    def to_dict(self, keys: Optional[List[str]] = None) -> Dict[str, Any]:
        dict_capture_session = {col.name: getattr(self, col.name) for col in self.__table__.columns}
        if keys is None:
            result = dict_capture_session
        else:
            result = {key: dict_capture_session[key] for key in keys}

        return result


class DeepweedModelRecommendation(Base, HasCreatedUpdated):
    __tablename__ = "deepweed_model_recommendations"
    id = Column(String, primary_key=True, default=uuid4_str)
    crop_id = Column(String, ForeignKey("crops.id"), nullable=False)
    geohash = Column(String, nullable=False)
    algorithm_name = Column(String, nullable=False)
    model_id = Column(String)
    model_parameters = Column(JSONB)

    crop_obj = relationship("Crop")

    def to_dict(self, keys: Optional[List[str]] = None) -> Dict[str, Any]:
        dict_deepweed_model_recommendation = {col.name: getattr(self, col.name) for col in self.__table__.columns}
        if keys is None:
            result = dict_deepweed_model_recommendation
        else:
            result = {key: dict_deepweed_model_recommendation[key] for key in keys}

        return result


class DeepweedModelMetadata(Base, HasCreatedUpdated):
    __tablename__ = "deepweed_model_metadata"
    id = Column(String, primary_key=True, default=uuid4_str)
    model_id = Column(String, ForeignKey("models.id"), nullable=False)
    metadata_json = Column(JSONB)
    test_results_json = Column(JSONB)
    wandb_json = Column(JSONB)
    embeddings = Column(Boolean, nullable=False)


class ModelIssue(Base, HasCreatedUpdated):
    __tablename__ = "model_issues"
    id = Column(String, primary_key=True, default=uuid4_str)
    issue_id = Column(String, nullable=True)
    model_id = Column(String, nullable=True)
    implements = Column(String, nullable=True)
    key = Column(String, nullable=True)
    created_in_jira = Column(BigInteger, nullable=True)


class LabelPoint(Base, HasCreatedUpdated):
    __tablename__ = "label_points"
    id = Column(String, primary_key=True, default=uuid4_str)
    label_id = Column(String, ForeignKey("labels.id"), nullable=False, index=True)
    image_id = Column(String, ForeignKey("images.id"), nullable=True, index=True)
    point_category_id = Column(String, ForeignKey("point_categories.id"), nullable=False)
    x = Column(Float, nullable=False)
    y = Column(Float, nullable=False)
    radius = Column(Float, nullable=False)
    confidence = Column(BigInteger, nullable=False)

    label = relationship("Label", foreign_keys=[label_id])
    image = relationship("Image", foreign_keys=[image_id])
    point_category = relationship("PointCategory", foreign_keys=[point_category_id])

    embeddings = relationship("Embedding")

    def to_dict(self, keys: Optional[List[str]] = None) -> Dict[str, Any]:
        dict_trigger = {col.name: getattr(self, col.name) for col in self.__table__.columns}
        if keys is None:
            result = dict_trigger
        else:
            result = {key: dict_trigger[key] for key in keys}

        return result


class PredictionPoint(Base, HasCreatedUpdated):
    __tablename__ = "prediction_points"
    id = Column(String, primary_key=True, default=uuid4_str)
    prediction_id = Column(String, ForeignKey("predictions.id"), nullable=False, index=True)
    point_category_id = Column(String, ForeignKey("point_categories.id"), nullable=False)
    x = Column(Float, nullable=False)
    y = Column(Float, nullable=False)
    radius = Column(Float, nullable=False)
    scores = Column(JSONB, nullable=False)

    prediction = relationship("Prediction", back_populates="points")


class BadLabelCandidate(Base, HasCreatedUpdated):
    __tablename__ = "bad_label_candidates"
    id = Column(String, primary_key=True, default=uuid4_str)
    image_id = Column(String, ForeignKey("images.id"), nullable=False)
    label_id = Column(String, ForeignKey("labels.id"), nullable=False)
    key = Column(String, nullable=False)
    candidate = Column(Boolean, nullable=False)


class PredictBurst(Base, HasCreatedUpdated):
    __tablename__ = "predict_bursts"
    id = Column(String, primary_key=True, default=uuid4_str)
    center_image_id = Column(String, ForeignKey("images.id"), nullable=False)
    url = Column(String)


class PlantCaptcha(Base, HasCreatedUpdated):
    __tablename__ = "plant_captchas"
    id = Column(String, primary_key=True, default=uuid4_str)
    name = Column(String, nullable=False)
    url = Column(String, nullable=False)
    model_id = Column(String, ForeignKey("models.id"), nullable=False)
    crop_id = Column(String, ForeignKey("crops.id"), nullable=False)
    robot_id = Column(String, nullable=False)
    metadata_json = Column(JSONB, nullable=False)
    captured_at = Column(BigInteger, nullable=False)

    def to_dict(self, keys: Optional[List[str]] = None) -> Dict[str, Any]:
        dict_plant_captcha = {col.name: getattr(self, col.name) for col in self.__table__.columns}
        if keys is None:
            result = dict_plant_captcha
        else:
            result = {key: dict_plant_captcha[key] for key in keys}

        return result


class PlantCaptchaItem(Base, HasCreatedUpdated):
    __tablename__ = "plant_captcha_items"
    id = Column(String, primary_key=True, default=uuid4_str)
    plant_captcha_id = Column(String, ForeignKey("plant_captchas.id"), nullable=False)
    initial_label = Column(String, nullable=False)
    operator_label = Column(String, nullable=True)
    url = Column(String, nullable=False)
    metadata_json = Column(JSONB, nullable=False)

    def to_dict(self, keys: Optional[List[str]] = None) -> Dict[str, Any]:
        dict_plant_captcha_item = {col.name: getattr(self, col.name) for col in self.__table__.columns}
        if keys is None:
            result = dict_plant_captcha_item
        else:
            result = {key: dict_plant_captcha_item[key] for key in keys}

        return result


class Embedding(Base, HasCreatedUpdated):
    __tablename__ = "embeddings"
    id = Column(String, primary_key=True, default=uuid4_str)
    point_id = Column(String, ForeignKey("label_points.id"), nullable=False)
    model_id = Column(String, ForeignKey("models.id"), nullable=False)
    type = Column(String, nullable=False)
    embedding = Column(ARRAY(Float), nullable=False)
    data = Column(Vector(1024), nullable=True)

    point = relationship("LabelPoint")
    model = relationship("Model")


class TaskV2(Base, HasCreatedUpdated):
    __tablename__ = "task_v2"
    id = Column(String, primary_key=True, default=uuid4_str)
    type = Column(Enum(constants.TaskTypes), nullable=False, index=True)

    completed_on = Column(BigInteger, nullable=True)  # epoch timestamp when the task was completed

    # family_id groups related tasks together
    family_id = Column(
        String, nullable=False, index=True, default=lambda context: context.get_current_parameters()["id"]
    )
    parent_id = Column(String, ForeignKey("task_v2.id"), nullable=True)
    child_id = Column(String, ForeignKey("task_v2.id"), nullable=True, index=True)
    image_id = Column(String, ForeignKey("images.id"), nullable=False, index=True)

    invalidated_by = Column(String, ForeignKey("users.id"), nullable=True)
    invalidated_on = Column(BigInteger, nullable=True)
    invalidate_reason = Column(Enum(constants.InvalidateReason), nullable=True)

    quarantine_reason = Column(Enum(constants.QuarantineReason), nullable=True)
    quarantined_on = Column(BigInteger, nullable=True)

    reserved_by = Column(String, ForeignKey("users.id"), nullable=True)
    reserved_on = Column(BigInteger, nullable=True)

    is_done = Column(Boolean, nullable=False, default=False, index=True)

    parent_task = relationship("TaskV2", foreign_keys=[parent_id])
    child_task = relationship("TaskV2", foreign_keys=[child_id])
    image = relationship("Image")
    invalidated_by_user = relationship("User", foreign_keys=[invalidated_by])
    reserved_by_user = relationship("User", foreign_keys=[reserved_by])


class CategoriesV2(Base, HasCreatedUpdated):
    __tablename__ = "categories_v2"
    id = Column(String, primary_key=True, default=uuid4_str)
    name = Column(String, nullable=False)

    classification_type = Column(Enum(constants.ClassificationType), nullable=False)

    # Category name and label type must be unique - this allows different types for the same name, but name should be unique within a type
    __table_args__ = (UniqueConstraint("name", "classification_type", name="uix_name_classification_type"),)


class FurrowLabel(Base, HasCreatedUpdated):
    __tablename__ = "furrow_label"

    id = Column(String, primary_key=True, default=uuid4_str)

    task_id = Column(String, ForeignKey("task_v2.id"), nullable=False, index=True)
    image_id = Column(String, ForeignKey("images.id"), nullable=False)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)

    # The line labels are stored as a list of LineLabel objects [<LineLabel>]
    line_labels = Column(JSONB, nullable=False, default=lambda: [])

    # Indicated if the label can be used in production
    is_production = Column(Boolean, nullable=False, default=False)
    # Indicated if the label is still valid - mainly controlled by an admin.
    is_valid = Column(Boolean, nullable=False, default=True)
    invalidated_on = Column(BigInteger, nullable=True)
    invalidate_reason = Column(Enum(constants.InvalidateReason), nullable=True)

    task = relationship("TaskV2")
    image = relationship("Image")
    user = relationship("User")


class PredictionPointClassification(Base, HasCreatedUpdated):
    __tablename__ = "prediction_point_classifications"
    id = Column(String, primary_key=True, default=uuid4_str)
    prediction_point_id = Column(String, ForeignKey("prediction_points.id"), nullable=False)
    prediction_point_classification_session_id = Column(
        String, ForeignKey("prediction_point_classification_sessions.id"), nullable=False
    )
    classification = Column(String, nullable=True)
    matching_prediction_point_id = Column(String, ForeignKey("prediction_points.id"), nullable=True)
    similarity_score = Column(Float, nullable=True)
    prediction_point = relationship("PredictionPoint", foreign_keys=[prediction_point_id])
    matching_prediction_point = relationship("PredictionPoint", foreign_keys=[matching_prediction_point_id])
    prediction_point_classification_session = relationship("PredictionPointClassificationSession")

    prediction_point_session_constraint = UniqueConstraint(
        prediction_point_id, prediction_point_classification_session_id, name="prediction_point_session_constraint"
    )


"""
This table stores the experiment sessions for prediction point classification.

LONG_RUNNING sessions are used for experiments that span multiple days or weeks, and is kept active to classify new points that get ingested.
ONE_OFF sessions are used to experiment one a single batch of points and are not kept active.
"""


class PredictionPointClassificationSession(Base, HasCreatedUpdated):
    __tablename__ = "prediction_point_classification_sessions"
    id = Column(String, primary_key=True, default=uuid4_str)
    robot_ids = Column(PG_ARRAY(String), nullable=True)  # Null robot_ids refers to all robots

    # model_id and category_collection_profile_id are used to identify the model and category collection profile used for the classification
    model_id = Column(String, ForeignKey("models.id"), nullable=False)
    category_collection_profile_id = Column(String, nullable=False)
    category_collection_profile_checksum = Column(String, nullable=False)

    category_collection_profile = Column(JSONB, nullable=False)
    last_accessed = Column(BigInteger, default=epoch_timestamp_ms, index=True)
    count_points_to_load = Column(Integer, nullable=False, default=0)
    support_set_ready = Column(Boolean, nullable=False, default=False)

    prediction_point_classifications = relationship("PredictionPointClassification")
    model = relationship("Model")


class FocusScore(Base, HasCreatedUpdated):
    __tablename__ = "focus_scores"
    id = Column(String, primary_key=True, default=uuid4_str)
    image_id = Column(String, ForeignKey("images.id"), nullable=False)
    sample_frequency = Column(Float, nullable=False)
    score = Column(Float, nullable=False)

    image = relationship("Image")


class ComparisonLabelingDataset(Base, HasCreatedUpdated):
    __tablename__ = "comparison_labeling_dataset"
    id = Column(String, primary_key=True, default=uuid4_str)
    dataset_id = Column(String, ForeignKey("datasets.id"))
    valid = Column(Boolean)
