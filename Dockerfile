# == REACT APP SETUP == #
FROM public.ecr.aws/docker/library/node:20.13.1-alpine AS react-setup
LABEL org.opencontainers.image.source=https://github.com/carbonrobotics/veselka

WORKDIR /veselka/frontend/app

COPY frontend/app/package.json frontend/app/yarn.lock ./

RUN set -ex; \
yarn install --frozen-lockfile --network-timeout 600000; \
yarn cache clean;

COPY frontend/app .

ARG SENTRY_AUTH_TOKEN
ENV SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN

# == REACT APP BUILDER == #
FROM react-setup AS react-builder
RUN yarn lint
RUN yarn build

# == VUE APP == #
FROM public.ecr.aws/docker/library/node:12 AS ui-builder
LABEL org.opencontainers.image.source=https://github.com/carbonrobotics/veselka

WORKDIR /build

COPY frontend/web/package*.json ./

RUN npm install -g serve && npm install

# Smaller containers
COPY frontend/web .

# Lint and build frontend
RUN npm run lint
RUN npm run build --verbose


# == FULLY CONTAINED WEB SERVICE == #
FROM public.ecr.aws/docker/library/python:3.10.6-bullseye
RUN apt-get update && apt-get -y install tmux vim gunicorn python3-gunicorn libgl1-mesa-glx curl

# Multiprocessing and cache directories
ENV PROMETHEUS_MULTIPROC_DIR=/multiproc-tmp
ENV MPLCONFIGDIR=/data/matplotlib
ENV PYTHONPATH=/veselka/backend:/veselka/dl_metrics:/veselka/dl_datasets

WORKDIR /veselka

# Install server requirements
COPY backend/requirements.txt backend/requirements.txt
RUN pip install -r backend/requirements.txt --timeout=300

RUN mkdir -p /multiproc-tmp

ARG GIT_SHA="unknown"
ENV GIT_SHA=$GIT_SHA

COPY backend backend
COPY dl_metrics dl_metrics
COPY frontend/ui frontend/ui
COPY alembic alembic
COPY alembic.ini alembic.ini

# vue app
COPY --from=ui-builder /build/dist /veselka/frontend/web
# react app
COPY --from=react-builder /veselka/frontend/app/build /veselka/frontend/app

RUN chmod -R 777 /multiproc-tmp
USER 65534
WORKDIR /data

CMD ["/veselka/backend/run"]
